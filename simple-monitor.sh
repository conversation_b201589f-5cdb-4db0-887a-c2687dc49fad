#!/bin/bash

# 简单的系统监控脚本
# 在Agent安装成功前的临时方案

clear
echo "🖥️  服务器监控面板 - 临时方案"
echo "=================================="
echo "⏰ 监控时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# CPU信息
echo "💻 CPU使用率:"
cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
echo "   当前使用率: ${cpu_usage}%"
echo ""

# 内存信息
echo "🧠 内存使用情况:"
free -h | grep -E "Mem|Swap"
echo ""

# 磁盘使用情况
echo "💾 磁盘使用情况:"
df -h | grep -E "/$|/home|/var"
echo ""

# 网络连接
echo "🌐 网络连接:"
echo "   活跃连接数: $(netstat -an | grep ESTABLISHED | wc -l)"
echo ""

# 系统负载
echo "⚡ 系统负载:"
uptime
echo ""

# 哪吒面板状态
echo "📊 哪吒面板状态:"
cd /root/fuwuqi/nezha
if docker compose -f docker-compose-final.yml ps | grep -q "Up"; then
    echo "   ✅ 哪吒面板: 运行正常"
    echo "   🌐 Web界面: http://74.48.157.54:8008"
else
    echo "   ❌ 哪吒面板: 未运行"
fi
echo ""

echo "💡 提示: 这是临时监控方案，Agent安装后将有完整的Web监控界面"
echo "🔄 每30秒自动刷新，按Ctrl+C退出"
