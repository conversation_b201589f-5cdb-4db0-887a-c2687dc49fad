#!/bin/bash

# 哪吒Agent一键安装脚本
# 配置信息
PANEL_HOST="************"
PANEL_PORT="5555"
SECRET="aSMEFVbBmSEYcVEto7"
TLS="false"

echo "🚀 哪吒Agent一键安装脚本"
echo "面板地址: ${PANEL_HOST}:${PANEL_PORT}"
echo "密钥: ${SECRET}"
echo "TLS: ${TLS}"
echo "=================================="

# 检查系统架构
ARCH=$(uname -m)
case $ARCH in
    x86_64)
        ARCH="amd64"
        ;;
    aarch64)
        ARCH="arm64"
        ;;
    armv7l)
        ARCH="arm"
        ;;
    *)
        echo "❌ 不支持的系统架构: $ARCH"
        exit 1
        ;;
esac

echo "📋 系统架构: $ARCH"

# 创建nezha用户
if ! id "nezha" &>/dev/null; then
    echo "👤 创建nezha用户..."
    useradd -r -d /opt/nezha -s /bin/bash nezha
fi

# 创建目录
echo "📁 创建目录..."
mkdir -p /opt/nezha/agent
cd /opt/nezha/agent

# 下载Agent
echo "⬇️  下载哪吒Agent..."

# 尝试多个下载源
DOWNLOAD_SUCCESS=false

# 下载源列表
DOWNLOAD_URLS=(
    "https://github.com/nezhahq/nezha/releases/latest/download/nezha-agent_linux_${ARCH}.tar.gz"
    "https://github.com/naiba/nezha/releases/latest/download/nezha-agent_linux_${ARCH}.tar.gz"
    "https://github.com/nezhahq/nezha/releases/download/v0.20.13/nezha-agent_linux_${ARCH}.tar.gz"
    "https://cdn.jsdelivr.net/gh/nezhahq/nezha@master/script/nezha-agent_linux_${ARCH}.tar.gz"
)

for url in "${DOWNLOAD_URLS[@]}"; do
    echo "🔗 尝试下载: $url"
    if wget -O nezha-agent.tar.gz "$url" 2>/dev/null; then
        if [ -s nezha-agent.tar.gz ]; then
            echo "✅ 下载成功"
            DOWNLOAD_SUCCESS=true
            break
        else
            echo "❌ 下载的文件为空"
            rm -f nezha-agent.tar.gz
        fi
    else
        echo "❌ 下载失败"
    fi
done

if [ "$DOWNLOAD_SUCCESS" = false ]; then
    echo "❌ 所有下载源都失败，尝试使用curl..."
    
    for url in "${DOWNLOAD_URLS[@]}"; do
        echo "🔗 使用curl尝试: $url"
        if curl -L -o nezha-agent.tar.gz "$url" 2>/dev/null; then
            if [ -s nezha-agent.tar.gz ]; then
                echo "✅ curl下载成功"
                DOWNLOAD_SUCCESS=true
                break
            else
                echo "❌ 下载的文件为空"
                rm -f nezha-agent.tar.gz
            fi
        else
            echo "❌ curl下载失败"
        fi
    done
fi

if [ "$DOWNLOAD_SUCCESS" = false ]; then
    echo "❌ 无法下载哪吒Agent，请检查网络连接"
    echo "💡 您可以手动下载并放置到 /opt/nezha/agent/ 目录"
    exit 1
fi

# 解压
echo "📦 解压Agent..."
if tar -xzf nezha-agent.tar.gz; then
    echo "✅ 解压成功"
    chmod +x nezha-agent
else
    echo "❌ 解压失败"
    exit 1
fi

# 创建systemd服务文件
echo "⚙️  创建systemd服务..."
cat > /etc/systemd/system/nezha-agent.service << EOF
[Unit]
Description=Nezha Agent
After=network.target

[Service]
Type=simple
User=nezha
WorkingDirectory=/opt/nezha/agent
ExecStart=/opt/nezha/agent/nezha-agent -s ${PANEL_HOST}:${PANEL_PORT} -p ${SECRET} --tls=${TLS}
Restart=on-failure
RestartSec=5s
KillMode=mixed

[Install]
WantedBy=multi-user.target
EOF

# 设置权限
echo "🔐 设置权限..."
chown -R nezha:nezha /opt/nezha

# 启动服务
echo "🚀 启动哪吒Agent服务..."
systemctl daemon-reload
systemctl enable nezha-agent
systemctl start nezha-agent

# 等待服务启动
sleep 3

# 检查状态
if systemctl is-active --quiet nezha-agent; then
    echo ""
    echo "🎉 哪吒Agent安装成功！"
    echo "📊 服务状态:"
    systemctl status nezha-agent --no-pager -l
    echo ""
    echo "💡 现在可以在哪吒面板中看到监控数据了！"
    echo "🌐 访问: http://************:8008"
else
    echo ""
    echo "❌ 哪吒Agent启动失败"
    echo "📋 服务状态:"
    systemctl status nezha-agent --no-pager -l
    echo ""
    echo "📋 服务日志:"
    journalctl -u nezha-agent --no-pager -l --lines=20
fi
