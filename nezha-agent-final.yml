version: '3.8'

services:
  nezha-agent:
    image: nezhahq/agent:latest
    container_name: nezha-agent
    restart: unless-stopped
    command: ["-s", "************:5555", "-p", "aSMEFVbBmSEYcVEto7", "--tls=false"]
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/host/root:ro
    environment:
      - HOST_PROC=/host/proc
      - HOST_SYS=/host/sys
      - HOST_ROOT=/host/root
    network_mode: host
    privileged: true
