#!/usr/bin/env python3
"""
哪吒面板模拟Agent - 用于测试和演示
"""

import time
import json
import psutil
import socket
import threading
from datetime import datetime

class MockNezhAgent:
    def __init__(self, server_host="************", server_port=5555, secret="aSMEFVbBmSEYcVEto7"):
        self.server_host = server_host
        self.server_port = server_port
        self.secret = secret
        self.running = False
        
    def get_system_info(self):
        """获取系统信息"""
        try:
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_total = memory.total
            memory_used = memory.used
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_total = disk.total
            disk_used = disk.used
            
            # 网络信息
            net_io = psutil.net_io_counters()
            
            # 系统负载
            load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else (0, 0, 0)
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count
                },
                'memory': {
                    'percent': memory_percent,
                    'total': memory_total,
                    'used': memory_used,
                    'available': memory.available
                },
                'disk': {
                    'percent': disk_percent,
                    'total': disk_total,
                    'used': disk_used,
                    'free': disk.free
                },
                'network': {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv
                },
                'load': {
                    'load1': load_avg[0],
                    'load5': load_avg[1],
                    'load15': load_avg[2]
                }
            }
        except Exception as e:
            print(f"获取系统信息失败: {e}")
            return None
    
    def connect_to_panel(self):
        """连接到哪吒面板"""
        try:
            print(f"🔗 尝试连接到哪吒面板: {self.server_host}:{self.server_port}")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            
            result = sock.connect_ex((self.server_host, self.server_port))
            
            if result == 0:
                print("✅ 连接成功！")
                return sock
            else:
                print(f"❌ 连接失败，错误代码: {result}")
                return None
                
        except Exception as e:
            print(f"❌ 连接异常: {e}")
            return None
    
    def send_heartbeat(self, sock):
        """发送心跳数据"""
        try:
            system_info = self.get_system_info()
            if system_info:
                # 构造简单的数据包
                data = {
                    'type': 'heartbeat',
                    'secret': self.secret,
                    'data': system_info
                }
                
                message = json.dumps(data).encode('utf-8')
                sock.send(message)
                print(f"📊 发送监控数据: CPU {system_info['cpu']['percent']:.1f}%, 内存 {system_info['memory']['percent']:.1f}%")
                return True
        except Exception as e:
            print(f"❌ 发送数据失败: {e}")
            return False
    
    def run(self):
        """运行模拟Agent"""
        print("🚀 启动哪吒面板模拟Agent")
        print(f"📡 服务器: {self.server_host}:{self.server_port}")
        print(f"🔑 密钥: {self.secret}")
        print("=" * 50)
        
        self.running = True
        
        while self.running:
            try:
                # 连接到面板
                sock = self.connect_to_panel()
                if not sock:
                    print("⏳ 10秒后重试连接...")
                    time.sleep(10)
                    continue
                
                # 发送心跳数据
                while self.running:
                    if not self.send_heartbeat(sock):
                        break
                    time.sleep(5)  # 每5秒发送一次数据
                    
                sock.close()
                
            except KeyboardInterrupt:
                print("\n🛑 收到停止信号，正在退出...")
                self.running = False
                break
            except Exception as e:
                print(f"❌ 运行异常: {e}")
                print("⏳ 10秒后重试...")
                time.sleep(10)
    
    def stop(self):
        """停止Agent"""
        self.running = False

def main():
    print("🎯 哪吒面板模拟Agent")
    print("这是一个用于测试的模拟Agent，可以向面板发送系统监控数据")
    print()
    
    # 检查psutil是否可用
    try:
        import psutil
        print("✅ psutil库可用，可以获取系统信息")
    except ImportError:
        print("❌ 缺少psutil库，请安装: pip3 install psutil")
        return
    
    # 创建并运行模拟Agent
    agent = MockNezhAgent()
    
    try:
        agent.run()
    except KeyboardInterrupt:
        print("\n👋 再见！")
        agent.stop()

if __name__ == "__main__":
    main()
