#!/bin/bash

# 哪吒Agent管理脚本

SERVICE_NAME="nezha-agent-py"
SCRIPT_PATH="/root/fuwuqi/nezha-agent-service.py"

show_status() {
    echo "📊 哪吒Agent服务状态:"
    echo "=================================="
    systemctl status $SERVICE_NAME --no-pager -l
    echo ""
    echo "📋 最近日志:"
    journalctl -u $SERVICE_NAME --no-pager -l --lines=5
}

show_logs() {
    echo "📋 哪吒Agent实时日志 (按Ctrl+C退出):"
    echo "=================================="
    journalctl -u $SERVICE_NAME -f
}

start_service() {
    echo "🚀 启动哪吒Agent服务..."
    systemctl start $SERVICE_NAME
    sleep 2
    show_status
}

stop_service() {
    echo "🛑 停止哪吒Agent服务..."
    systemctl stop $SERVICE_NAME
    sleep 1
    echo "✅ 服务已停止"
}

restart_service() {
    echo "🔄 重启哪吒Agent服务..."
    systemctl restart $SERVICE_NAME
    sleep 2
    show_status
}

show_info() {
    echo "ℹ️  哪吒面板信息:"
    echo "=================================="
    echo "🌐 Web界面: http://************:8008"
    echo "📡 gRPC端口: ************:5555"
    echo "🔑 密钥: aSMEFVbBmSEYcVEto7"
    echo "🐍 Agent类型: Python版 (简化版)"
    echo "📁 脚本位置: $SCRIPT_PATH"
    echo "⚙️  服务名称: $SERVICE_NAME"
    echo ""
    echo "🚀 管理命令:"
    echo "   systemctl start $SERVICE_NAME    # 启动服务"
    echo "   systemctl stop $SERVICE_NAME     # 停止服务"
    echo "   systemctl restart $SERVICE_NAME  # 重启服务"
    echo "   systemctl status $SERVICE_NAME   # 查看状态"
    echo "   journalctl -u $SERVICE_NAME -f   # 查看日志"
}

case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    info)
        show_info
        ;;
    *)
        echo "🎯 哪吒Agent管理脚本"
        echo "用法: $0 {start|stop|restart|status|logs|info}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动Agent服务"
        echo "  stop    - 停止Agent服务"
        echo "  restart - 重启Agent服务"
        echo "  status  - 查看服务状态"
        echo "  logs    - 查看实时日志"
        echo "  info    - 显示系统信息"
        echo ""
        show_status
        ;;
esac
