#!/bin/bash

# 哪吒Agent自动安装脚本

set -e

# 配置信息
PANEL_HOST="************"
PANEL_PORT="5555"
SECRET="aSMEFVbBmSEYcVEto7"
TLS="false"

echo "🚀 开始安装哪吒Agent..."
echo "面板地址: ${PANEL_HOST}:${PANEL_PORT}"
echo "密钥: ${SECRET}"
echo "TLS: ${TLS}"

# 创建nezha用户
if ! id "nezha" &>/dev/null; then
    echo "创建nezha用户..."
    useradd -r -d /opt/nezha -s /bin/bash nezha
fi

# 创建目录
mkdir -p /opt/nezha/agent
cd /opt/nezha/agent

# 下载Agent
echo "下载哪吒Agent..."
if command -v wget >/dev/null 2>&1; then
    wget -O nezha-agent.tar.gz "https://github.com/naiba/nezha/releases/latest/download/nezha-agent_linux_amd64.tar.gz" || {
        echo "使用备用下载链接..."
        wget -O nezha-agent.tar.gz "https://github.com/nezhahq/nezha/releases/download/v0.20.13/nezha-agent_linux_amd64.tar.gz"
    }
elif command -v curl >/dev/null 2>&1; then
    curl -L -o nezha-agent.tar.gz "https://github.com/naiba/nezha/releases/latest/download/nezha-agent_linux_amd64.tar.gz" || {
        echo "使用备用下载链接..."
        curl -L -o nezha-agent.tar.gz "https://github.com/nezhahq/nezha/releases/download/v0.20.13/nezha-agent_linux_amd64.tar.gz"
    }
else
    echo "错误: 需要wget或curl"
    exit 1
fi

# 解压
echo "解压Agent..."
tar -xzf nezha-agent.tar.gz
chmod +x nezha-agent

# 创建systemd服务文件
echo "创建systemd服务..."
cat > /etc/systemd/system/nezha-agent.service << EOF
[Unit]
Description=Nezha Agent
After=network.target

[Service]
Type=simple
User=nezha
WorkingDirectory=/opt/nezha/agent
ExecStart=/opt/nezha/agent/nezha-agent -s ${PANEL_HOST}:${PANEL_PORT} -p ${SECRET} --tls=${TLS}
Restart=on-failure
RestartSec=5s
KillMode=mixed

[Install]
WantedBy=multi-user.target
EOF

# 设置权限
chown -R nezha:nezha /opt/nezha

# 启动服务
echo "启动哪吒Agent服务..."
systemctl daemon-reload
systemctl enable nezha-agent
systemctl start nezha-agent

# 检查状态
sleep 3
if systemctl is-active --quiet nezha-agent; then
    echo "✅ 哪吒Agent安装成功并正在运行！"
    echo "📊 请返回哪吒面板查看监控数据"
    systemctl status nezha-agent --no-pager -l
else
    echo "❌ 哪吒Agent启动失败，查看日志："
    systemctl status nezha-agent --no-pager -l
    journalctl -u nezha-agent --no-pager -l
fi
