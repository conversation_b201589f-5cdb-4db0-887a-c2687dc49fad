# 哪吒面板 - 添加服务器监控指南

## 🎯 当前状态
✅ 哪吒面板已成功安装并运行  
✅ GitHub OAuth认证已配置  
✅ 用户已成功登录  
❓ 需要添加服务器进行监控  

## 📊 为什么页面是空白的？
这是正常现象！哪吒面板是一个服务器监控系统，需要添加服务器后才会显示监控数据。就像一个空的仪表盘，需要连接设备后才会有数据显示。

## 🖥️ 添加第一台服务器（Web界面操作）

### 步骤1：进入服务器管理
1. 点击页面顶部的 **"服务器"** 菜单
2. 点击 **"新增"** 按钮

### 步骤2：填写服务器信息
```
名称: 主服务器
分组: 默认
排序: 1  
备注: 主要监控服务器
```

### 步骤3：获取连接信息
添加后会显示：
- **服务器ID**: (自动生成)
- **密钥**: (自动生成的长字符串)
- **连接地址**: ************:5555

## 🔧 安装Agent（命令行操作）

添加服务器后，使用以下命令安装Agent：

```bash
# 下载安装脚本
curl -L https://raw.githubusercontent.com/naiba/nezha/master/script/install.sh -o nezha-agent.sh
chmod +x nezha-agent.sh

# 安装Agent (需要从Web界面获取密钥)
./nezha-agent.sh install_agent
```

安装时需要输入：
- **面板地址**: `************:5555`
- **密钥**: 从Web界面复制的密钥
- **是否启用TLS**: 选择 `n` (否)

## 🎉 完成后的效果

安装Agent后，您将看到：
- 📊 实时CPU、内存、磁盘使用率
- 📈 历史监控图表
- 🌐 网络流量统计
- 💾 系统信息详情
- ⚡ 实时状态更新

## 🚀 快速测试

如果您想快速看到效果，可以：
1. 先在Web界面添加服务器
2. 告诉我生成的密钥
3. 我帮您自动安装Agent

这样几分钟内就能看到监控数据了！
