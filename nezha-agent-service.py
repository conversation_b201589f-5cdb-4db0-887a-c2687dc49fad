#!/usr/bin/env python3
"""
哪吒面板Agent服务 - 简化版本
用于向哪吒面板发送系统监控数据
"""

import time
import json
import psutil
import socket
import threading
import subprocess
import os
from datetime import datetime

class NezhAgentService:
    def __init__(self):
        self.server_host = "************"
        self.server_port = 5555
        self.secret = "aSMEFVbBmSEYcVEto7"
        self.running = False
        self.server_id = 1
        
    def get_system_stats(self):
        """获取系统统计信息"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            
            # 网络信息
            net_io = psutil.net_io_counters()
            
            # 系统负载
            try:
                load_avg = os.getloadavg()
            except:
                load_avg = (0, 0, 0)
            
            # 系统信息
            boot_time = psutil.boot_time()
            
            return {
                'timestamp': int(time.time()),
                'cpu': cpu_percent,
                'memory_total': memory.total,
                'memory_used': memory.used,
                'swap_total': memory.total,
                'swap_used': memory.used,
                'disk_total': disk.total,
                'disk_used': disk.used,
                'net_in_transfer': net_io.bytes_recv,
                'net_out_transfer': net_io.bytes_sent,
                'net_in_speed': 0,
                'net_out_speed': 0,
                'uptime': int(time.time() - boot_time),
                'load1': load_avg[0],
                'load5': load_avg[1],
                'load15': load_avg[2],
                'tcp_conn_count': len(psutil.net_connections(kind='tcp')),
                'udp_conn_count': len(psutil.net_connections(kind='udp')),
                'process_count': len(psutil.pids())
            }
        except Exception as e:
            print(f"获取系统信息失败: {e}")
            return None
    
    def send_heartbeat(self):
        """发送心跳数据到面板"""
        try:
            stats = self.get_system_stats()
            if not stats:
                return False
            
            # 构造数据包
            data = {
                'type': 'heartbeat',
                'server_id': self.server_id,
                'secret': self.secret,
                'data': stats
            }
            
            # 连接到面板
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            
            try:
                sock.connect((self.server_host, self.server_port))
                
                # 发送数据
                message = json.dumps(data).encode('utf-8')
                sock.send(message)
                
                print(f"✅ 发送监控数据: CPU {stats['cpu']:.1f}%, 内存 {(stats['memory_used']/stats['memory_total']*100):.1f}%, 磁盘 {(stats['disk_used']/stats['disk_total']*100):.1f}%")
                
                sock.close()
                return True
                
            except Exception as e:
                print(f"❌ 连接失败: {e}")
                sock.close()
                return False
                
        except Exception as e:
            print(f"❌ 发送心跳失败: {e}")
            return False
    
    def run_service(self):
        """运行Agent服务"""
        print("🚀 启动哪吒Agent服务")
        print(f"📡 面板地址: {self.server_host}:{self.server_port}")
        print(f"🔑 密钥: {self.secret}")
        print(f"🆔 服务器ID: {self.server_id}")
        print("=" * 60)
        
        self.running = True
        failed_count = 0
        
        while self.running:
            try:
                if self.send_heartbeat():
                    failed_count = 0
                    time.sleep(5)  # 成功后等待5秒
                else:
                    failed_count += 1
                    if failed_count >= 3:
                        print(f"⚠️  连续失败{failed_count}次，等待30秒后重试...")
                        time.sleep(30)
                        failed_count = 0
                    else:
                        time.sleep(10)  # 失败后等待10秒
                        
            except KeyboardInterrupt:
                print("\n🛑 收到停止信号，正在退出...")
                self.running = False
                break
            except Exception as e:
                print(f"❌ 服务异常: {e}")
                time.sleep(10)
    
    def stop_service(self):
        """停止服务"""
        self.running = False

def create_systemd_service():
    """创建systemd服务文件"""
    service_content = f"""[Unit]
Description=Nezha Agent Service (Python)
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory={os.getcwd()}
ExecStart=/usr/bin/python3 {os.path.abspath(__file__)}
Restart=on-failure
RestartSec=10s
KillMode=mixed

[Install]
WantedBy=multi-user.target
"""
    
    try:
        with open('/etc/systemd/system/nezha-agent-py.service', 'w') as f:
            f.write(service_content)
        
        # 重新加载systemd
        subprocess.run(['systemctl', 'daemon-reload'], check=True)
        subprocess.run(['systemctl', 'enable', 'nezha-agent-py'], check=True)
        
        print("✅ systemd服务文件已创建")
        print("🚀 使用以下命令管理服务:")
        print("   启动: systemctl start nezha-agent-py")
        print("   停止: systemctl stop nezha-agent-py")
        print("   状态: systemctl status nezha-agent-py")
        print("   日志: journalctl -u nezha-agent-py -f")
        
        return True
    except Exception as e:
        print(f"❌ 创建systemd服务失败: {e}")
        return False

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--install-service':
        print("📦 安装systemd服务...")
        if create_systemd_service():
            print("✅ 服务安装完成！")
        return
    
    print("🎯 哪吒面板Agent服务 (Python版)")
    print("这是一个简化版的Agent，用于向哪吒面板发送监控数据")
    print()
    
    # 检查依赖
    try:
        import psutil
        print("✅ psutil库可用")
    except ImportError:
        print("❌ 缺少psutil库，请安装: pip3 install psutil")
        return
    
    # 创建并运行Agent
    agent = NezhAgentService()
    
    try:
        agent.run_service()
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        agent.stop_service()

if __name__ == "__main__":
    main()
