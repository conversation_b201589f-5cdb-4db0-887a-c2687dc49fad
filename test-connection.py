#!/usr/bin/env python3
"""
简单的哪吒面板连接测试脚本
"""

import socket
import time
import json

def test_grpc_connection():
    """测试gRPC连接"""
    host = "************"
    port = 5555
    
    print(f"🔍 测试连接到哪吒面板: {host}:{port}")
    
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        # 尝试连接
        result = sock.connect_ex((host, port))
        
        if result == 0:
            print("✅ gRPC端口连接成功！")
            print("📊 面板可以接收Agent连接")
            
            # 发送简单的测试数据
            test_data = b"nezha-test-connection"
            sock.send(test_data)
            
            sock.close()
            return True
        else:
            print(f"❌ 连接失败，错误代码: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

def test_web_interface():
    """测试Web界面"""
    import urllib.request
    
    try:
        url = "http://************:8008"
        print(f"🌐 测试Web界面: {url}")
        
        response = urllib.request.urlopen(url, timeout=10)
        if response.getcode() == 200:
            print("✅ Web界面访问正常")
            return True
        else:
            print(f"❌ Web界面返回状态码: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"❌ Web界面访问异常: {e}")
        return False

def main():
    print("🚀 哪吒面板连接测试")
    print("=" * 50)
    
    # 测试Web界面
    web_ok = test_web_interface()
    print()
    
    # 测试gRPC连接
    grpc_ok = test_grpc_connection()
    print()
    
    print("📋 测试结果:")
    print(f"   Web界面: {'✅ 正常' if web_ok else '❌ 异常'}")
    print(f"   gRPC端口: {'✅ 正常' if grpc_ok else '❌ 异常'}")
    
    if web_ok and grpc_ok:
        print("\n🎉 哪吒面板运行正常！")
        print("💡 Agent安装后即可看到监控数据")
    else:
        print("\n⚠️  存在连接问题，请检查配置")

if __name__ == "__main__":
    main()
