#!/bin/bash

# 哪吒面板完整管理脚本

DASHBOARD_DIR="/root/fuwuqi/nezha"
AGENT_DIR="/opt/nezha/agent"

show_status() {
    echo "🎯 哪吒监控系统状态"
    echo "=================================="
    
    echo "📊 面板状态:"
    cd $DASHBOARD_DIR
    if docker compose -f docker-compose-final.yml ps | grep -q "Up"; then
        echo "   ✅ 哪吒面板: 运行正常"
        echo "   🌐 Web界面: http://74.48.157.54:8008"
        echo "   📡 gRPC端口: 74.48.157.54:5555"
    else
        echo "   ❌ 哪吒面板: 未运行"
    fi
    
    echo ""
    echo "🤖 Agent状态:"
    if systemctl is-active --quiet nezha-agent; then
        echo "   ✅ 哪吒Agent: 运行正常"
        echo "   📋 服务状态:"
        systemctl status nezha-agent --no-pager -l | head -10
    else
        echo "   ❌ 哪吒Agent: 未运行"
    fi
}

manage_dashboard() {
    echo "📊 面板管理"
    echo "=================================="
    cd $DASHBOARD_DIR
    
    case "$1" in
        start)
            echo "🚀 启动哪吒面板..."
            docker compose -f docker-compose-final.yml up -d
            ;;
        stop)
            echo "🛑 停止哪吒面板..."
            docker compose -f docker-compose-final.yml down
            ;;
        restart)
            echo "🔄 重启哪吒面板..."
            docker compose -f docker-compose-final.yml restart
            ;;
        logs)
            echo "📋 面板日志:"
            docker compose -f docker-compose-final.yml logs -f
            ;;
        *)
            echo "用法: $0 dashboard {start|stop|restart|logs}"
            ;;
    esac
}

manage_agent() {
    echo "🤖 Agent管理"
    echo "=================================="
    
    case "$1" in
        start)
            echo "🚀 启动哪吒Agent..."
            systemctl start nezha-agent
            ;;
        stop)
            echo "🛑 停止哪吒Agent..."
            systemctl stop nezha-agent
            ;;
        restart)
            echo "🔄 重启哪吒Agent..."
            systemctl restart nezha-agent
            ;;
        logs)
            echo "📋 Agent日志:"
            journalctl -u nezha-agent -f
            ;;
        config)
            echo "⚙️  Agent配置:"
            sudo cat $AGENT_DIR/config.yml
            ;;
        *)
            echo "用法: $0 agent {start|stop|restart|logs|config}"
            ;;
    esac
}

show_info() {
    echo "ℹ️  哪吒监控系统信息"
    echo "=================================="
    echo "🌐 Web界面: http://74.48.157.54:8008"
    echo "👤 管理员: HuangYuXin (GitHub OAuth)"
    echo "📡 gRPC端口: 74.48.157.54:5555"
    echo "🔑 服务器密钥: aSMEFVbBmSEYcVEto7"
    echo ""
    echo "📁 文件位置:"
    echo "   面板配置: $DASHBOARD_DIR"
    echo "   Agent配置: $AGENT_DIR"
    echo ""
    echo "🚀 管理命令:"
    echo "   $0 status           # 查看系统状态"
    echo "   $0 dashboard start  # 启动面板"
    echo "   $0 agent restart    # 重启Agent"
    echo "   $0 logs            # 查看所有日志"
    echo ""
    echo "🔧 系统服务:"
    echo "   systemctl status nezha-agent    # Agent状态"
    echo "   docker compose -f $DASHBOARD_DIR/docker-compose-final.yml ps  # 面板状态"
}

show_logs() {
    echo "📋 系统日志"
    echo "=================================="
    echo "最近的Agent日志:"
    journalctl -u nezha-agent --no-pager -l --lines=10
    echo ""
    echo "最近的面板日志:"
    cd $DASHBOARD_DIR
    docker compose -f docker-compose-final.yml logs --tail=10
}

case "$1" in
    status)
        show_status
        ;;
    dashboard)
        manage_dashboard "$2"
        ;;
    agent)
        manage_agent "$2"
        ;;
    info)
        show_info
        ;;
    logs)
        show_logs
        ;;
    *)
        echo "🎯 哪吒监控系统管理脚本"
        echo "用法: $0 {status|dashboard|agent|info|logs}"
        echo ""
        echo "命令说明:"
        echo "  status              - 查看系统状态"
        echo "  dashboard <action>  - 管理面板 (start|stop|restart|logs)"
        echo "  agent <action>      - 管理Agent (start|stop|restart|logs|config)"
        echo "  info               - 显示系统信息"
        echo "  logs               - 查看系统日志"
        echo ""
        show_status
        ;;
esac
