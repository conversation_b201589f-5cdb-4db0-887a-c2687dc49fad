# 哪吒监控面板

## 📊 简介
哪吒监控是一个轻量级的服务器监控面板，支持：
- 多服务器监控
- 实时性能监控
- 告警通知
- 简洁美观的界面
- 支持多种部署方式

## 🚀 部署说明

### 方式一：Docker Compose部署（推荐）
```bash
# 启动服务
docker compose up -d

# 查看状态
docker compose ps

# 查看日志
docker compose logs -f
```

### 方式二：一键安装脚本
```bash
# 运行安装脚本
chmod +x nezha.sh
./nezha.sh
```

## 📋 访问信息

### Web界面
- **访问地址**: http://74.48.157.54:8008
- **gRPC端口**: 5555（用于Agent连接）

### 首次配置
1. 访问Web界面
2. 配置OAuth认证（GitHub/GitLab等）
3. 添加服务器监控

## ⚙️ OAuth配置

### GitHub OAuth配置
1. 访问 https://github.com/settings/applications/new
2. 创建新的OAuth应用
3. 填写以下信息：
   - Application name: 哪吒监控
   - Homepage URL: http://your-domain:8008
   - Authorization callback URL: http://your-domain:8008/oauth2/callback
4. 获取Client ID和Client Secret
5. 更新docker-compose.yml中的配置

### GitLab OAuth配置
1. 访问GitLab设置页面
2. 创建新的应用程序
3. 配置回调URL和权限
4. 获取应用ID和密钥

## 🔧 Agent安装

### 在被监控服务器上安装Agent
```bash
# 下载安装脚本
curl -L https://raw.githubusercontent.com/naiba/nezha/master/script/install.sh -o nezha.sh

# 运行安装（仅安装Agent）
chmod +x nezha.sh
./nezha.sh install_agent
```

### Agent配置
- 服务器地址: your-domain:5555
- 通信密钥: 在面板中生成
- TLS: 根据需要启用

## 📊 监控功能

### 系统监控
- CPU使用率
- 内存使用情况
- 磁盘使用情况
- 网络流量
- 系统负载

### 网络监控
- 延迟监控
- 丢包率检测
- 网络质量评估

### 告警功能
- 邮件通知
- Webhook通知
- 自定义告警规则

## 🛠️ 管理命令

```bash
# 启动服务
docker compose up -d

# 停止服务
docker compose down

# 重启服务
docker compose restart

# 查看日志
docker compose logs -f nezha-dashboard

# 更新服务
docker compose pull && docker compose up -d
```

## 🔒 安全配置

### 访问控制
- 配置OAuth认证
- 设置管理员权限
- 启用HTTPS

### 防火墙设置
- 开放8008端口（Web界面）
- 开放5555端口（gRPC通信）
- 限制访问来源IP

## 📈 性能优化

### 数据库优化
- 定期清理历史数据
- 优化数据库索引
- 配置数据保留策略

### 网络优化
- 使用CDN加速
- 配置反向代理
- 启用数据压缩

## 🛠️ 故障排除

### 常见问题
1. **OAuth配置错误**
   - 检查Client ID和Secret
   - 验证回调URL配置
   - 确认OAuth应用权限

2. **Agent连接失败**
   - 检查网络连接
   - 验证通信密钥
   - 确认端口开放

3. **数据不更新**
   - 检查Agent状态
   - 验证时间同步
   - 查看错误日志

### 日志查看
```bash
# 查看面板日志
docker compose logs nezha-dashboard

# 查看Agent日志
journalctl -u nezha-agent
```

## 📚 更多资源

- [官方文档](https://nezha.wiki/)
- [GitHub仓库](https://github.com/naiba/nezha)
- [社区讨论](https://github.com/naiba/nezha/discussions)
- [问题反馈](https://github.com/naiba/nezha/issues)
