services:
  nezha-dashboard:
    image: ghcr.io/naiba/nezha-dashboard
    container_name: nezha-dashboard
    restart: unless-stopped
    volumes:
      - ./data:/dashboard/data
    ports:
      - "8008:80"
      - "5555:5555"
    environment:
      # 管理员用户配置
      GIN_MODE: release
      # 数据库配置（使用SQLite）
      NZ_SQLITE_PATH: /dashboard/data/sqlite.db
      # OAuth配置 - 使用GitHub OAuth
      NZ_OAUTH2_TYPE: "github"
      NZ_GITHUB_OAUTH_CLIENT_ID: "********************"
      NZ_GITHUB_OAUTH_CLIENT_SECRET: "e8ef4a30acfc2978f867ae602c01f648e36430e4"
      # 站点配置
      NZ_SITE_TITLE: "哪吒监控面板"
      NZ_ADMIN_LOGINS: "admin"
      # gRPC端口
      NZ_GRPC_PORT: 5555
      # 其他配置
      NZ_DISABLE_COMMAND_EXECUTE: false
      NZ_CUSTOM_CODE_DASHBOARD: ""
      NZ_THEME: "default"

volumes:
  nezha-data:
