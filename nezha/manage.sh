#!/bin/bash

# 哪吒面板管理脚本
# 用于快速管理哪吒面板服务

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "哪吒面板管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  start     启动哪吒面板服务"
    echo "  stop      停止哪吒面板服务"
    echo "  restart   重启哪吒面板服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  test      测试服务连接"
    echo "  info      显示访问信息"
    echo "  help      显示此帮助信息"
}

# 启动服务
start_service() {
    print_message $BLUE "🚀 启动哪吒面板服务..."
    docker compose -f docker-compose-final.yml up -d
    print_message $GREEN "✅ 哪吒面板服务已启动"
    show_access_info
}

# 停止服务
stop_service() {
    print_message $YELLOW "⏹️  停止哪吒面板服务..."
    docker compose -f docker-compose-final.yml down
    print_message $GREEN "✅ 哪吒面板服务已停止"
}

# 重启服务
restart_service() {
    print_message $BLUE "🔄 重启哪吒面板服务..."
    docker compose -f docker-compose-final.yml restart
    print_message $GREEN "✅ 哪吒面板服务已重启"
    show_access_info
}

# 查看状态
show_status() {
    print_message $BLUE "📊 哪吒面板服务状态:"
    docker compose -f docker-compose-final.yml ps
}

# 查看日志
show_logs() {
    print_message $BLUE "📋 哪吒面板服务日志:"
    docker compose -f docker-compose-final.yml logs -f
}

# 测试连接
test_connection() {
    print_message $BLUE "🔍 测试哪吒面板连接..."
    
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8008 | grep -q "200"; then
        print_message $GREEN "✅ 哪吒面板Web界面正常运行"
        print_message $GREEN "   HTTP状态码: 200"
    else
        print_message $RED "❌ 哪吒面板Web界面连接失败"
        return 1
    fi
}

# 显示访问信息
show_access_info() {
    local server_ip=$(hostname -I | awk '{print $1}')
    
    print_message $GREEN "🌐 哪吒面板访问信息:"
    print_message $BLUE "   Web界面: http://${server_ip}:8008"
    print_message $BLUE "   gRPC端口: 5555 (Agent连接)"
    print_message $YELLOW "   首次访问需要配置OAuth认证"
    print_message $YELLOW "   建议配置GitHub OAuth或其他认证方式"
}

# 显示系统信息
show_info() {
    show_access_info
    echo ""
    show_status
    echo ""
    test_connection
}

# 主逻辑
case "${1:-help}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    test)
        test_connection
        ;;
    info)
        show_info
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $RED "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
