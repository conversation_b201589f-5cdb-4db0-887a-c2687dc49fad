#!/bin/bash

# 哪吒面板OAuth配置更新脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🔧 哪吒面板OAuth配置更新工具"
echo ""

print_message $GREEN "✅ 已检测到GitHub OAuth配置："
print_message $BLUE "   Client ID: Ov23116QrSguPJ9z1Ze4"
print_message $BLUE "   Callback URL: http://************:8008/oauth2/callback"
print_message $BLUE "   Admin User: l<PERSON><PERSON><PERSON>hanng"
echo ""

print_message $YELLOW "⚠️  请在GitHub页面点击 'Generate a new client secret' 按钮"
print_message $YELLOW "   然后将生成的Client Secret粘贴到下面："
echo ""

# 读取Client Secret
read -p "请输入GitHub Client Secret: " client_secret

if [ -z "$client_secret" ]; then
    print_message $RED "❌ Client Secret不能为空！"
    exit 1
fi

print_message $BLUE "🔄 正在更新配置文件..."

# 更新docker-compose.yml
sed -i "s/YOUR_CLIENT_SECRET_HERE/$client_secret/g" docker-compose.yml
print_message $GREEN "✅ 已更新 docker-compose.yml"

# 更新.env文件
sed -i "s/YOUR_CLIENT_SECRET_HERE/$client_secret/g" .env
print_message $GREEN "✅ 已更新 .env"

# 更新config.yaml
sed -i "s/YOUR_CLIENT_SECRET_HERE/$client_secret/g" config.yaml
print_message $GREEN "✅ 已更新 config.yaml"

echo ""
print_message $GREEN "🎉 OAuth配置更新完成！"
echo ""

print_message $BLUE "📋 下一步操作："
print_message $YELLOW "1. 启动哪吒面板服务："
echo "   ./manage.sh start"
echo ""
print_message $YELLOW "2. 访问Web界面："
echo "   http://************:8008"
echo ""
print_message $YELLOW "3. 使用GitHub账号登录："
echo "   点击GitHub登录按钮，使用您的GitHub账号 (liuguanzhanng) 登录"
echo ""

# 询问是否立即启动服务
read -p "是否现在启动哪吒面板服务？(y/n): " start_now

if [[ $start_now =~ ^[Yy]$ ]]; then
    print_message $BLUE "🚀 正在启动哪吒面板服务..."
    ./manage.sh start
else
    print_message $BLUE "💡 您可以稍后使用以下命令启动服务："
    echo "   ./manage.sh start"
fi
